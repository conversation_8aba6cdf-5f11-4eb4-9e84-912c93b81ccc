package com.stpl.tech.vortex.service.controller;

import com.amazonaws.regions.Regions;
import com.google.gson.Gson;
import com.itextpdf.html2pdf.HtmlConverter;
import com.stpl.tech.kettle.core.notification.RefundNotification;
import com.stpl.tech.kettle.core.notification.RefundReceipt;
import com.stpl.tech.vortex.service.cache.BrandMetaDataCache;
import com.stpl.tech.vortex.service.cache.UnitCache;
import com.stpl.tech.common.exceptions.AuthenticationFailureException;
import com.stpl.tech.common.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.core.EmailStatus;
import com.stpl.tech.kettle.core.OrderEmailEntryType;
import com.stpl.tech.kettle.core.notification.CharityNotification;
import com.stpl.tech.kettle.core.notification.CharityReceipt;
import com.stpl.tech.kettle.core.notification.DeliveryNotification;
import com.stpl.tech.kettle.core.notification.DeliveryNotificationReceipt;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.notification.ReceiptNotification;
import com.stpl.tech.kettle.core.notification.SubscriptionInfo;
import com.stpl.tech.kettle.core.notification.SubscriptionNotification;
import com.stpl.tech.kettle.core.notification.SubscriptionReceipt;
import com.stpl.tech.kettle.core.notification.VerificationEmailNotification;
import com.stpl.tech.kettle.core.notification.VerificationTemplate;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderEmailNotification;
import com.stpl.tech.kettle.domain.Customer;
import com.stpl.tech.kettle.domain.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.EmailInfo;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.notification.OrderInfo;
import com.stpl.tech.kettle.notification.receipt.OrderEmailReceipt;
import com.stpl.tech.kettle.util.properties.OasisEnvironmentProperties;
import com.stpl.tech.master.core.external.inventory.service.SQSNotificationService;
import com.stpl.tech.master.core.external.notification.GoogleChatNotification;
import com.stpl.tech.master.core.external.notification.GoogleChatNotificationService;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.RandomStringGenerator;
import com.stpl.tech.vortex.service.notification.service.NotificationService;
import com.stpl.tech.vortex.service.notification.service.impl.PasswordImpl;
import com.stpl.tech.vortex.service.service.AuthorizationService;
import com.stpl.tech.vortex.service.service.CustomerOfferManagementService;
import com.stpl.tech.vortex.service.service.CustomerService;
import com.stpl.tech.vortex.service.service.OrderSearchService;
import com.stpl.tech.vortex.service.service.SubscriptionService;
import com.stpl.tech.vortex.service.utils.AppConstant;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.jms.JMSException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

@Log4j2
@RequestMapping(value = "v1/email")
@RestController
public class EmailResource {

    @Autowired
    private BrandMetaDataCache brandMetaDataCache;

    @Autowired
    private OrderSearchService orderSearchService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private UnitCache unitCache;

    @Autowired
    private AuthorizationService authorizationService;

    @Autowired
    private OasisEnvironmentProperties environmentProperties;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private SubscriptionService subscriptionService;

    @Autowired
    private CustomerOfferManagementService customerOfferManagementService;

    @Autowired
    private SQSNotificationService sqsNotificationService;

    @Value("${cloud.aws.region.static}")
    private String regionDetail;



    private static final RandomStringGenerator generator = new RandomStringGenerator();

 //   @Scheduled(fixedRate = 30000)

    @PostMapping(value = "send-email")
    public boolean sendReceiptEmails(@RequestBody EmailInfo emailInfo) throws JMSException {
        boolean isEmailSent = false;
        OrderEmailNotification detail = new OrderEmailNotification();
        log.info("email info For Resend : {} ", new Gson().toJson(emailInfo));
        if(emailInfo.isResend()){
            detail = orderSearchService.getResendEmailEvents(emailInfo.getOrderId(),emailInfo.getCustomerId(),emailInfo.getEmail());
        }
        else {
            detail = orderSearchService.getEmailEvents(emailInfo.getOrderId());
        }
        try {
        if (Objects.nonNull(detail) ) {
            log.info("Found emails to be delivered in the queue for orderId :{}", detail.getOrderId());

            Brand chaayos = brandMetaDataCache.getBrandMetaData().get(AppConstants.CHAAYOS_BRAND_ID);
            if (Objects.nonNull(detail.getEntryType()) && detail.getEntryType().equals(OrderEmailEntryType.ORDER.name())
                    || detail.getEntryType().equals(OrderEmailEntryType.ORDER_CANCELLATION.name())) {
                isEmailSent = sendReceiptNotification(detail);
            } else if (Objects.nonNull(detail.getEntryType()) && detail.getEntryType().equals(OrderEmailEntryType.SUBSCRIPTION.name())) {
                isEmailSent = sendSubscriptionReceiptNotification(detail);
            } else if (Objects.nonNull(detail.getEntryType()) && detail.getEntryType().equals(OrderEmailEntryType.DISPATCH_DELAY.name())
                    || detail.getEntryType().equals(OrderEmailEntryType.DELIVERY_DELAY.name())) {
                isEmailSent = sendDeliveryNotification(detail);
            } else if (Objects.nonNull(detail.getEntryType()) && detail.getEntryType().equals(OrderEmailEntryType.CHARITY_ORDER.name())) {
                isEmailSent = sendCharityOrderNotification(detail);
            } else if (Objects.nonNull(detail.getEntryType()) && detail.getEntryType().equals(OrderEmailEntryType.VERIFICATION.name())) {
                try {
                    isEmailSent = sendVerificationNotification(detail, chaayos.getVerificationEmailTemplate());
                } catch (Exception e) {
                    isEmailSent = false;
                    log.error("Encountered error while sending email to :::::: {}", detail.getEmailAddress(), e);
                }
            }
        }else {
            log.error("Can not find email notification detail for orderId :{}",emailInfo.getOrderId());
             OrderDetail orderDetail = orderSearchService.getOrder(emailInfo.getOrderId());
            if(Objects.isNull(orderDetail)){
                log.error("Can not find order detail for orderId :{}",emailInfo.getOrderId());
                isEmailSent = false;
            }else{
                return false;
            }
        }
        } catch (Exception e) {
            log.error("Error while Sending Order Email ::: {} ",e);
            isEmailSent = false;
        }
        if(!isEmailSent){
            sendFailureNotification(detail,emailInfo, new Exception("Email not sent"));
        }
        return true;
    }

    public Boolean sendReceiptNotification(OrderEmailNotification detail) {
        String generatedToken = null;
        try {
            Integer customerId = orderSearchService.getCustomerId(detail.getOrderId());
            long startTime = System.currentTimeMillis();
            if (detail.getEntryType().equals(OrderEmailEntryType.ORDER.name()) && !AppUtils.isValidEmail(detail.getEmailAddress())) {
                customerService.removeIncorrectEmailInfo(customerId);
                orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.INCORRECT_MAIL, "WRONG Mail address");
                return false;
            }
            if (detail.getEntryType().equals(OrderEmailEntryType.ORDER_CANCELLATION.name())) {
                String[] emailAddressList = detail.getEmailAddress().split(",");
                boolean emailFlag = false;
                for (String emailId : emailAddressList) {
                    if (!AppUtils.isValidEmail(emailId)) {
                        emailFlag = true;
                        break;
                    }
                }
                if (emailFlag) {
                    orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.INCORRECT_MAIL, "WRONG Mail address");
                    return false;
                }
            }
            if (AppConstants.EXCLUDE_CUSTOMER_IDS.contains(customerId)) {
                orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.NOT_TO_BE_DELIVERED,
                        "EMAIL NOT BE SEND");
                orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.SUCCESS,
                        AppConstant.EMAILSUCCESS);
                return false;
                }
                  OrderInfo orderInfo = orderSearchService.getOrderReceipt(detail.getOrderId(), false, null);

            if (!AppConstants.getValue(detail.getIsEmailVerified()) && orderInfo.getOrder().getCustomerId() > 5) {
                if (orderInfo.getOrder().getCustomerId() > 5) {
                    Customer customer = orderInfo.getCustomer();
                    generatedToken = authorizationService.findAuthorizationToken(customer.getEmailId());
                    if (generatedToken == null) {
                        generatedToken = getAuthorizationToken(detail, customer.getContactNumber());
                    }
                }
            }
            boolean cancellation = OrderEmailEntryType.ORDER_CANCELLATION.name().equals(detail.getEntryType());
            boolean isEmailVerified = AppConstants.getValue(detail.getIsEmailVerified());
            String verifyEmailLink = generatedToken == null || cancellation ? null : environmentProperties.getVerifyEmailPath();
            String fromEmail = (AppUtils.isDev(environmentProperties.getEnvironmentType()) ? EnvType.DEV.name() + " " : "")
                    + AppConstants.CHAAYOS_RECEIPT;
            Unit unit = unitCache.getUnitById(orderInfo.getOrder().getUnitId());

            String toEmail = cancellation ? detail.getEmailAddress() + "," + environmentProperties.getRecieptEmail()
                    : environmentProperties.getRecieptEmail();
            CustomerEmailData customerEmailData = customerOfferManagementService
                    .getCustomerEmailData(orderInfo.getCustomer().getId(), orderInfo.getOrder().getBrandId());
            if(org.apache.commons.lang3.StringUtils.isNotBlank(detail.getEmailAddress()) &&
                    !detail.getEmailAddress().equalsIgnoreCase(orderInfo.getCustomer().getEmailId()) && !cancellation){
                toEmail  = toEmail + "," + detail.getEmailAddress();
            }
            ReceiptNotification notification = new ReceiptNotification(
                    new OrderEmailReceipt(environmentProperties.getChaayosBaseUrl(), unit, orderInfo,
                            AppUtils.getFormattedEmail(fromEmail, environmentProperties.getRecieptEmail()), toEmail,
                            environmentProperties.getBasePath(), isEmailVerified, verifyEmailLink, generatedToken,
                            environmentProperties.getBillPromotion(), cancellation, customerEmailData),
                    sendEmailToCustomer(orderInfo.getOrder()));
            notification.sendEmail();
            if (!cancellation && environmentProperties.isKioskOrderPDFReceiptSMS() && orderInfo.getOrder().getCustomerId() > 5
                    && StringUtils.isNotBlank(orderInfo.getOrder().getSourceId())
                    && StringUtils.startsWith(orderInfo.getOrder().getSourceId(), "KSK")) {
                createReceiptPdf(notification.body(), orderInfo.getOrder().getGenerateOrderId(),
                        orderInfo.getOrder().getCustomerName(), orderInfo.getCustomer().getContactNumber(), unit);
            }
            orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.SUCCESS,
                    AppConstant.EMAILSUCCESS);
            log.info("Email sent in {}: ", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("Error while sending email for order email productId " + detail.getOrderEmailId(), e);
            orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.FAILED, e.getMessage());
            return false;
        }
        return true;
    }


    public Boolean sendSubscriptionReceiptNotification(OrderEmailNotification detail) {

        try {
            SubscriptionInfo orderInfo = subscriptionService.getSubscriptionInfo(detail.getOrderId());
            String fromEmail = (AppUtils.isDev(environmentProperties.getEnvironmentType()) ? EnvType.DEV.name() + " " : "")
                    + AppConstants.CHAAYOS_SUBSCRIPTION;
            SubscriptionNotification notification = new SubscriptionNotification(
                    new SubscriptionReceipt(orderInfo, environmentProperties.getBasePath(),
                            AppUtils.getFormattedEmail(fromEmail, environmentProperties.getRecieptEmail()), environmentProperties.getRecieptEmail()));
            notification.sendEmail();
            orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.SUCCESS,
                    "Successfully Delivered the subscription email");
        } catch (Exception e) {
            log.error("Error while sending email for subscription email productId " + detail.getOrderEmailId(), e);
            orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.FAILED, e.getMessage());
            return false;
        }
        return true;

    }

    public Boolean sendDeliveryNotification(OrderEmailNotification detail) {
        try {
            OrderInfo orderInfo = orderSearchService.getOrderReceipt(detail.getOrderId(), false, null);
            String fromEmail = (AppUtils.isDev(environmentProperties.getEnvironmentType()) ? EnvType.DEV.name() + " " : "")
                    + AppConstants.CHAAYOS_DELIVERY;
            DeliveryNotification notification = new DeliveryNotification(
                    OrderEmailEntryType.valueOf(detail.getEntryType()), environmentProperties.getDeliverySupportEmailId(),
                    environmentProperties.getDeliverySupportSecondaryEmailId(),
                    new DeliveryNotificationReceipt(orderInfo, environmentProperties.getBasePath(),
                            AppUtils.getFormattedEmail(fromEmail, environmentProperties.getDeliverySupportEmailId()),
                            detail.getEmailAddress(), OrderEmailEntryType.valueOf(detail.getEntryType())));
            if (Boolean.TRUE.equals(environmentProperties.getDeliveryDelayEmailTrigger())) {
                notification.sendEmail();
                orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.SUCCESS,
                        "Successfully Delivered the delivery/dispatch delay email");
            }
            if (detail.getEntryType().equals(OrderEmailEntryType.DELIVERY_DELAY.name())
                    && Boolean.TRUE.equals(environmentProperties.getDeliveryDelaySlackTrigger())) {
                GoogleChatNotificationService.getInstance().sendNotification(environmentProperties.getEnvironmentType(),
                        ApplicationName.KETTLE_SERVICE.name(), GoogleChatNotification.DELAY_DELIVERY_CHANNEL,
                        String.valueOf(orderInfo.getUnit().getManagerId()), notification);
            }
        } catch (Exception e) {
            log.error("Error while sending delivery/dispatch email for order email id " + detail.getOrderEmailId(), e);
            orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.FAILED, e.getMessage());
            return false;
        }
        return true;
    }

    public Boolean sendCharityOrderNotification(OrderEmailNotification detail) {
        try {
            OrderInfo orderInfo = orderSearchService.getOrderReceipt(detail.getOrderId(), false, null);
            String fromEmail = (AppUtils.isDev(environmentProperties.getEnvironmentType()) ? EnvType.DEV.name() + " " : "")
                    + AppConstants.CHAAYOS_RECEIPT;
            CharityNotification notification = new CharityNotification(
                    new CharityReceipt(orderInfo, environmentProperties.getBasePath(),
                            AppUtils.getFormattedEmail(fromEmail, environmentProperties.getRecieptEmail()), detail.getEmailAddress()));
            notification.sendEmail();
            orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.SUCCESS,
                    "Successfully Delivered the charity order email");
        } catch (Exception e) {
            log.error("Error while sending email for charity order  " + detail.getOrderEmailId(), e);
            orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.FAILED, e.getMessage());
            return false;
        }
        return true;
    }


    private boolean sendVerificationNotification(OrderEmailNotification detail, String verificationTemplate)
            throws AuthenticationFailureException, UnsupportedEncodingException, DataNotFoundException {
        if (!AppConstants.getValue(detail.getIsEmailVerified())) {
            String generatedToken = getAuthorizationToken(detail, detail.getContact());
            VerificationTemplate template = new VerificationTemplate(environmentProperties.getBasePath(), environmentProperties.getVerifyEmailPath(),
                    generatedToken, verificationTemplate);
            VerificationEmailNotification notification = new VerificationEmailNotification(template,
                    environmentProperties.getEnvironmentType(), detail.getEmailAddress(), detail.getCustomerName());
            try {
                notification.sendEmail();
                orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.SUCCESS,
                        AppConstant.EMAILSUCCESS);
                return true;
            } catch (EmailGenerationException e) {
                log.error("Encountered error while generating email for :::: {}", detail.getEmailAddress(), e);
                orderSearchService.updateStatus(detail.getOrderEmailId(), EmailStatus.FAILED, e.getMessage());
                return false;
            }

        }
        return false;
    }

    // send refund email
    private String getAuthorizationToken(OrderEmailNotification detail, String contactNumber)
            throws AuthenticationFailureException {
        authorizationService.expireAuthorizationRequest(detail.getEmailAddress());
        String token = generator.getRandonNumber(10);
        String decryptedString = contactNumber + "|" + detail.getEmailAddress() + "|" + token;
        String generatedToken = PasswordImpl.encrypt(decryptedString);
        generatedToken = URLEncoder.encode(generatedToken, StandardCharsets.UTF_8);
        authorizationService.createEmailAuthorizationRequest(detail.getEmailAddress(), token, generatedToken);
        return generatedToken;
    }

    private boolean sendEmailToCustomer(Order order) {
        return order.getSubscriptionDetail() == null
                || (order.getSubscriptionDetail() != null && order.getSubscriptionDetail().isEmailNotification());
    }

    private void createReceiptPdf(String receiptContent, String orderId, String customerName, String contact,
                                  Unit unit) {
        try {
            if (StringUtils.isNotBlank(receiptContent) && StringUtils.isNotBlank(orderId)) {
                String kioskPath = environmentProperties.getBasePath() + "/" + unit.getId() + "/kiosk/orders";
                File kioskFolder = new File(kioskPath);
                if (!kioskFolder.exists()) {
                    kioskFolder.mkdirs();
                }
                String fileName = "orderReceipt-" + orderId + ".pdf";
                String receiptPath = kioskPath + "/" + fileName;
                File pdfFile = new File(receiptPath);
                if (!pdfFile.exists()) {
                    pdfFile.createNewFile();
                }
                try (OutputStream outputStream = new FileOutputStream(pdfFile)) {
                    HtmlConverter.convertToPdf(receiptContent, outputStream);
                    outputStream.flush();

                    String baseDir = "kiosk/" + unit.getId() + "/orders";
                    try {
                        FileDetail s3File = fileArchiveService.saveFileToS3(environmentProperties.getS3Bucket(), baseDir, pdfFile,
                                true);
                        if (s3File != null) {
                            String receiptLink = environmentProperties.getReceiptDownloadBaseUrl() + "wcrt/or/" + orderId + "/dld";
                            sendOrderPlacedNotification(contact, customerName, receiptLink, orderId, unit.getName());
                        } else {
                            log.error("Error uploading report to S3.");
                        }
                        pdfFile.delete();
                    } catch (Exception e) {
                        log.error("Encountered error while uploading report to S3", e);
                        if(pdfFile.exists()) {
                            pdfFile.delete();
                        }
                    }

                } catch (IOException e) {
                    log.error("Exception Occurred while converting html to pdf");
                    if(pdfFile.exists()) {
                        pdfFile.delete();
                    }
                }
            }
        } catch (Exception ex) {
            log.error("Exception Occurred while creating PDF of Receipt");
        }

    }

    public void sendOrderPlacedNotification(String customerNumber, String customerName, String receiptPDFUrl,
                                            String orderId, String unitName) {
        try {
            ShortUrlData shortUrl = SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(receiptPDFUrl);
            String message = String.format(
                    "Dear %s, thank you for placing an order at Chaayos, %s. Download bill for #%s here %s",
                    customerName, unitName, orderId, shortUrl.getUrl());
            notificationService.sendNotification("KIOSK_ORDER_PLACED", message, customerNumber,
                    SolsInfiniWebServiceClient.getTransactionalClient(), true,null);
        } catch (Exception e) {
            log.error("Error while sending notification to the customer", e);
        }
    }

    public void sendFailureNotification(OrderEmailNotification detail,EmailInfo emailInfo, Exception e) throws JMSException {

        if (Objects.nonNull(detail) && detail.getRetryCount() == environmentProperties.getRetryCount()) {
            String errorMsg = "Unable to send email to Customer Email Id: " + detail.getEmailAddress()
                    + " for Order Id: " + detail.getOrderId();
            new ErrorNotification("Receipt Email Failure", errorMsg, e, environmentProperties.getEnvironmentType()).sendEmail();
        }else {
            Regions region  = Regions.fromName(regionDetail);
            sqsNotificationService.publishToSQS(environmentProperties.getEnvironmentType().name(),new Gson().toJson(emailInfo),"_NOTIFICATION_EVENTS", region);
            }
        }


        @PostMapping(value = "send-email-wrt-date")
        public boolean sendReceiptEmailsWrtDate(@RequestBody String date) throws JMSException {
        Date filterDate = AppUtils.getDate(AppUtils.parseDate(date));
            List<OrderEmailNotification> orderEmailNotificationList = orderSearchService.getEmailEventsWrtDate(filterDate);
            log.info("Size Of Order emails :: {} " , orderEmailNotificationList.size());
            AtomicReference<Integer> countEmails= new AtomicReference<>(0);
            orderEmailNotificationList.forEach(orderEmailNotification -> {
                if (Boolean.TRUE.equals(sendReceiptNotification(orderEmailNotification))){
                   countEmails.getAndSet(countEmails.get() + 1);
                }
                    }
            );
            log.info("Number Of emails Sent : {} ",countEmails.get());
            if(countEmails.get() > 0){
                return  true;
            }
            return false;
        }
        }



