package com.stpl.tech.vortex.service.notification.service.impl;

import com.stpl.tech.vortex.service.cache.EnvironmentPropertiesCache;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.external.notification.service.MessagingClient;
import com.stpl.tech.master.data.model.NotificationLogDetail;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.vortex.service.notification.dao.master.NotificationLogDetailDao;
import com.stpl.tech.vortex.service.notification.service.NotificationService;
import com.stpl.tech.vortex.service.sqs.publisher.OutboundMessageProducer;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.jms.JMSException;
import java.io.IOException;
import java.util.Objects;

@Service
@Log4j2
public class NotificationServiceImpl implements NotificationService {

    @Autowired
    private NotificationLogDetailDao notificationLogDetailDao;

    @Autowired
    private OutboundMessageProducer outboundMessageProducer;

    @Autowired
    private EnvironmentPropertiesCache propertiesCache;

//    @Override
//    public boolean sendNotificationToCustomer(String communicationType, Object metadata) {
//
//        return false;
//    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean sendNotification(String type, String message, String contact, MessagingClient client,
                                    boolean sendNotification, NotificationPayload payload) throws IOException, JMSException {
        boolean status = false;
        try {
            if (Objects.nonNull(payload) && payload.isWhatsappOptIn() && sendWhatsappNotification(sendNotification,payload.isWhatsappOptIn(),payload.isSendWhatsapp())) {
                outboundMessageProducer.publishCustomerCommunicationEvent(payload);
                return true;
            }
        } catch (Exception e){
            log.error("Exception Faced While Sending Whatsapp Notification  {}",payload.getCustomerId());
        }
        if(propertiesCache.getInternalNos() != null && propertiesCache.getInternalNos().contains(contact)){
            log.info(" Message not sent to internal contact No : {}", contact);
            sendNotification = false;
        }

        if (sendNotification) {
            status = client.sendMessage(message, contact);
        } else {
            log.info("SMS Skipped, Message : {} \nContact Number : {}", message, contact);
            status = true;
        }
        try {
            createNotification(type, message, contact, client, status);
        } catch (Exception e) {
            log.error("Error while logging customer notification", e);
        }
        return status;
    }

    private boolean sendWhatsappNotification(Boolean props, Boolean whatsappOptIn, Boolean sendWhatsapp) {
        return props && (whatsappOptIn && sendWhatsapp);
    }

    private void createNotification(String type, String message, String contact, MessagingClient client,
                                    boolean sendNotification) {

        NotificationLogDetail notificationLogDetail = new NotificationLogDetail();
        notificationLogDetail.setType(type);
        notificationLogDetail.setNotificationTime(AppUtils.getCurrentTimestamp());
        notificationLogDetail.setMessage(message);
        notificationLogDetail.setContact(contact);
        notificationLogDetail.setNotificationSent(AppUtils.setStatus(sendNotification));
        notificationLogDetail.setServiceClient(client.getClass().getSimpleName());
        notificationLogDetailDao.save(notificationLogDetail);
    }

}
