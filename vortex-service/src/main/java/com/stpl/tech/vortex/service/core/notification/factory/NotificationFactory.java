package com.stpl.tech.vortex.service.core.notification.factory;

import com.stpl.tech.kettle.domain.model.NotificationType;
import com.stpl.tech.vortex.service.core.notification.adapter.EmailResourceAdapter;
import com.stpl.tech.vortex.service.core.notification.adapter.GchatResourceAdapter;
import com.stpl.tech.vortex.service.core.notification.adapter.NotificationAdapter;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class NotificationFactory {
    private EmailResourceAdapter emailResourceAdapter;

    private GchatResourceAdapter gchatResourceAdapter;

    public NotificationAdapter getAdapter(NotificationType notificationType) {
        switch (notificationType) {
            case EMAIL: {
                return emailResourceAdapter;
            }
            case GCHAT:{
                return gchatResourceAdapter;
            }
            
            default: {
                throw new IllegalArgumentException("Notification Type Of " + notificationType.name() + " Not Found");
            }
        }
    }
}
